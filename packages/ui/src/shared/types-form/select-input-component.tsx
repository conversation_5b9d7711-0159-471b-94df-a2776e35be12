'use client';

import { Typo<PERSON>, Toolt<PERSON> } from '@mui/joy';
import Link from '@mui/joy/Link';
import classNames from 'classnames';
import React, { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { useLocale } from '@bika/contents/i18n';
import { AvatarLogo } from '@bika/types/system';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import ChevronUpOutlined from '@bika/ui/icons/components/chevron_up_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import { NodeIcon } from '@bika/ui/node/icon';
import { type SelectInputProps } from './select-input';
import { PopoverListContent } from './select-input-item-component';
import {
  PopoverWithListNavigationContent,
  ********************************,
  PopoverWithListNavigation,
} from '../../components/popover';
import { FormHelperText, FormLabel, FormControl } from '../../form-components';
import { Box, Stack } from '../../layout-components';
import { EllipsisText } from '../../text';

export function SelectInputComponent<T extends string>(props: SelectInputProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');

  const ref = useRef<HTMLDivElement>(null);
  const { t } = useLocale();
  const {
    hidden,
    setErrors,
    required,
    disabled,
    disabledTips,
    id,
    helpText,
    iconSize,
    emptyMsg,
    selectedEmptyContent,
    hideLabel,
    hideSearch,
    autoFocus = true,
  } = props;

  useEffect(() => {
    const errorKey = `SelectInput-${id ?? props?.label}`;
    if (required && setErrors) {
      setErrors({ [errorKey]: props.value ? '' : t.integration.general.err_msg });
    }
    return () => {
      if (required && setErrors) {
        setErrors({ [errorKey]: '' });
      }
    };
  }, [props.value, setErrors, required]);

  // 保留选中的值
  const filteredOptions = useMemo(() => {
    let newOptions = props.options;
    if (props.hiddenOptions) {
      newOptions = props.options.filter((option) => !props.hiddenOptions?.includes(option.value));
    }

    if (!searchTerm) return newOptions;
    const lowerSearchTerm = searchTerm.toLowerCase();
    return newOptions.filter(
      (option) =>
        option.label?.toLowerCase().includes(lowerSearchTerm) ||
        option.description?.toLowerCase().includes(lowerSearchTerm),
    );
  }, [props.options, props.hiddenOptions, searchTerm]);

  // Filter out disabled options for keyboard navigation
  const disabledIndices = useMemo(
    () =>
      filteredOptions.reduce((indices: number[], option, index) => {
        if (option.disabled) {
          indices.push(index);
        }
        return indices;
      }, []),
    [filteredOptions],
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
      if (props.handleSearch) {
        props.handleSearch(e.target.value);
      }
      e.preventDefault();
      e.stopPropagation();
    },
    [props.handleSearch],
  );

  const handleSearchText = useCallback(
    (e: string) => {
      setSearchTerm(e);
      if (props.handleSearch) {
        props.handleSearch(e);
      }
    },
    [props.handleSearch],
  );

  function renderValue({ value: selectValue, disabled }: { value: T | null; disabled?: boolean }) {
    if (!selectValue) {
      return <p style={{ opacity: 0.9, color: 'var(--text-disabled)' }}>{props.placeholder}</p>;
    }

    const item = props.options.find((o) => o.value === selectValue);

    // Create icon element at the top
    let iconElement: React.ReactNode = null;
    if (item?.icon) {
      if (typeof item.icon === 'object' && 'type' in item.icon) {
        iconElement = (
          <NodeIcon
            value={
              {
                kind: 'avatar',
                avatar: item.icon as unknown as AvatarLogo,
              } as const
            }
            size={iconSize || (item.description ? 32 : 24)}
          />
        );
      } else if (typeof item.icon === 'string') {
        iconElement = (
          <NodeIcon
            value={{
              kind: 'avatar',
              avatar: {
                url: item.icon as string,
                type: 'URL',
              },
            }}
            size={iconSize || (item.description ? 32 : 24)}
          />
        );
      } else {
        iconElement = (
          <Box
            sx={{
              borderRadius: '3px',
              flex: '0 0 content',
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              mr: 1,
              '& > img': {
                width: iconSize || (item.description ? 32 : 24),
                height: iconSize || (item.description ? 32 : 24),
              },
              '& > .avatar': {
                width: iconSize || (item.description ? 32 : 24),
                height: iconSize || (item.description ? 32 : 24),
                fontSize: '12px',
              },
            }}
          >
            {React.cloneElement(item.icon as React.ReactNode, {
              size: iconSize || (item.description ? 32 : 24),
            })}
          </Box>
        );
      }
    }

    return (
      <Box
        display="flex"
        alignItems="center"
        className="w-full"
        sx={{
          opacity: disabled ? 0.5 : 1,
          cursor: disabled ? 'not-allowed' : 'pointer',
        }}
      >
        {iconElement}
        {item?.label ? (
          <EllipsisText>
            <Typography level="b3" textColor={'var(--text-primary)'} sx={{ flexGrow: 1, textAlign: 'left' }}>
              {item?.label}
            </Typography>
          </EllipsisText>
        ) : (
          selectedEmptyContent
        )}
      </Box>
    );
  }

  const [isOpen, setOpen] = useState(false);

  const handleChange = (val: T | null) => {
    if (!val) {
      props.onChange(null);
      return;
    }

    setSearchTerm('');

    // if multiple is false , close the popover
    setOpen(false);
    props.onChange(val as T);
  };

  // Handle open change
  const handleOpenChange = useCallback(
    (open: boolean) => {
      setOpen(open);
      if (open) {
        if (hideSearch) {
          setTimeout(() => {
            ref.current?.focus();
          }, 100);
        }
      }
    },
    [hideSearch],
  );

  return (
    <FormControl sx={{ mt: 1, display: hidden ? 'none' : 'flex', ...(props.classes?.root ?? {}) }}>
      {props.label && !hideLabel && (
        <FormLabel required={required}>
          {props.label}
          {props.helpLink && (
            <Tooltip title={props.helpLink.text} variant="solid" arrow color="neutral" placement="top">
              <Link
                href={props.helpLink.url}
                target="_blank"
                rel="noreferrer"
                endDecorator={props.helpLink.icon || <QuestionCircleOutlined color={'var(--text-secondary)'} />}
              />
            </Tooltip>
          )}
        </FormLabel>
      )}
      {helpText && <FormHelperText sx={{ mb: 1 }}>{helpText}</FormHelperText>}
      <PopoverWithListNavigation
        placement={'bottom-start'}
        matchWidth={true}
        open={isOpen}
        onOpenChange={handleOpenChange}
        zIndex={'var(--joy-zIndex-modal)'}
        modal={true}
        listNavigation={{
          loop: true,
          virtual: false,
          orientation: 'vertical',
          focusItemOnOpen: 'auto',
          // hideSearch ? 'auto' : false,
          focusItemOnHover: true,
          disabledIndices,
        }}
      >
        <******************************** asChild>
          <Stack
            direction={'row'}
            onClick={(e) => {
              if (disabled) return;
              setOpen(!isOpen);
              if (hideSearch) {
                setTimeout(() => {
                  ref.current?.focus();
                }, 100);
              }
              e.preventDefault();
              e.stopPropagation();
            }}
            alignItems={'center'}
            className={classNames(
              'zod-form-input-select w-full pr-[8px] cursor-pointer items-center justify-between items-center min-h-[40px] p-y-[4px]',
            )}
            sx={{
              backgroundColor: 'var(--bg-controls)',
              borderRadius: '4px',
              paddingInline: '16px',
              alignItems: 'center',
              ...(props.classes?.joySelect ?? {}),

              ...(isOpen
                ? {
                    backgroundColor: 'var(--bg-controls-hover)',
                    border: '1px solid var(--brand)',
                  }
                : {}),
            }}
          >
            <Stack
              className={classNames('flex grow-auto items-center items-center overflow-x-hidden gap-y-[4px] w-full')}
              direction={'row'}
              spacing={1}
            >
              {renderValue({
                disabled,
                value: props.value,
              })}
            </Stack>

            <Box className={'flex items-center justify-center flex-none ml-[4px]'}>
              {isOpen ? (
                <ChevronUpOutlined color={'var(--text-secondary)'} size={16} />
              ) : (
                <ChevronDownOutlined color={'var(--text-secondary)'} size={16} />
              )}
            </Box>
          </Stack>
        </********************************>

        <PopoverWithListNavigationContent className="Popover" ref={ref}>
          <PopoverListContent
            // ref={ref}
            filteredOptions={filteredOptions}
            searchTerm={searchTerm}
            hideSearch={hideSearch}
            autoFocus={autoFocus}
            emptyMsg={emptyMsg}
            selectedValue={props.value}
            iconSize={iconSize}
            disabledTips={disabledTips}
            itemContentSx={props.classes?.itemContentSx}
            popoverContainerSx={props.classes?.popoverContainer}
            footer={props.footer}
            onSearchChange={handleSearchText}
            onSelect={(value) => handleChange(value as T)}
          />
        </PopoverWithListNavigationContent>
      </PopoverWithListNavigation>
      {props.errorTips && <FormHelperText sx={{ color: 'var(--status-danger)' }}>{props.errorTips}</FormHelperText>}
    </FormControl>
  );
}
